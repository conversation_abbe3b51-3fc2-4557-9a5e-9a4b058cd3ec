version: "3"

includes:
  common: ./desktop/build/Taskfile.yml
  darwin: ./desktop/build/darwin/Taskfile.yml

vars:
  APP_NAME: ns-drive
  BIN_DIR: bin
  VITE_PORT: "{{.WAILS_VITE_PORT | default 9245}}"

tasks:
  # Desktop - Wails v3 tasks
  build:
    summary: Builds the application
    cmds:
      - task: "{{OS}}:build"
    dir: desktop

  package:
    summary: Packages a production build of the application
    cmds:
      - task: "{{OS}}:package"
    dir: desktop

  run:
    summary: Runs the application
    cmds:
      - task: "{{OS}}:run"
    dir: desktop

  dev:
    summary: Runs the application in development mode
    cmds:
      - go build -o bin/ns-drive
      - ./bin/ns-drive
    dir: desktop

  devs:
    summary: Runs the application with Wails v3 dev server
    cmds:
      - $(go env GOPATH)/bin/wails3 dev -config ./build/config.yml -port {{.VITE_PORT}}
    dir: desktop

  build-mac-v2:
    summary: Legacy Wails v2 macOS build
    platforms: [darwin]
    cmds:
      - rm -rf ./frontend/dist
      - wails build -v 2
      - rm -rf ../desktop.app
      - mv build/bin/desktop.app ../desktop.app
      # - codesign --sign - --force --deep build/bin/desktop.app
      # - upx --best build/bin/desktop --force-macos
      # - chmod +x build/bin/desktop.app/Contents/MacOS/desktop
      - open ../desktop.app
    dir: desktop

  build-win-v2:
    summary: Legacy Wails v2 Windows build
    platforms: [windows]
    cmds:
      - wails build -v 2 # -windowsconsole to show the console for debugging
      - powershell.exe "Move-Item -Path ./build/bin/desktop.exe -Destination ../desktop.exe -Force"
    dir: desktop

  # Frontend tasks (delegated to desktop build tasks)
  install:frontend:deps:
    summary: Install frontend dependencies
    cmds:
      - task: common:install:frontend:deps
    dir: desktop

  build:frontend:
    summary: Build frontend for production
    cmds:
      - task: common:build:frontend
    dir: desktop

  dev:frontend:
    summary: Build frontend for development with watch
    cmds:
      - task: common:dev:frontend
    dir: desktop

  # Development utilities
  generate:bindings:
    summary: Generate TypeScript bindings from Go code
    cmds:
      - task: common:generate:bindings
    dir: desktop

  clean:
    summary: Clean build artifacts
    cmds:
      - task: common:clean
    dir: desktop

  fmt:
    summary: Format Go code
    cmds:
      - task: common:fmt
    dir: desktop

  test:
    summary: Run tests
    cmds:
      - task: common:test
    dir: desktop
